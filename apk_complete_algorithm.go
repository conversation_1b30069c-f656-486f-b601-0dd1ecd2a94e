// Complete APK algorithm analysis based on deep reverse engineering
package main

import (
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

// APK's exact rounding algorithm (from CalendarUtil.roundedMinute)
func apkRoundedMinute(t time.Time) string {
	seconds := float64(t.Second())
	fractionalMinutes := seconds / 60.0
	roundedMinutes := math.Round(fractionalMinutes) // LibcRound equivalent

	baseTime := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	adjustedTime := baseTime.Add(time.Duration(roundedMinutes) * time.Minute)

	return adjustedTime.Format("15:04")
}

// Test different potential sources of the 1-minute difference
func testPotentialDifferences() {
	latitude := 23.118889
	longitude := 113.37

	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")

	// Focus on the problematic dates
	testDates := []struct {
		date     string
		expected map[string]string
	}{
		{"2025-08-03", map[string]string{"Asr": "15:56"}},
		{"2025-08-20", map[string]string{"Asr": "15:56"}},
		{"2025-08-14", map[string]string{"Isha": "20:14"}},
	}

	fmt.Println("🔍 TESTING POTENTIAL SOURCES OF 1-MINUTE DIFFERENCE")
	fmt.Println("=" + strings.Repeat("=", 80))

	for _, testCase := range testDates {
		date, _ := time.ParseInLocation("2006-01-02", testCase.date, shanghaiLocation)

		fmt.Printf("\n📅 %s\n", testCase.date)
		fmt.Println("-" + strings.Repeat("-", 60))

		// Test 1: Different elevation values
		fmt.Println("🏔️ Testing elevation effects:")
		elevations := []float64{0, 10, 20, 30, 40, 50}

		for _, elevation := range elevations {
			config := prayer.Config{
				Latitude:           latitude,
				Longitude:          longitude,
				Elevation:          elevation,
				Timezone:           shanghaiLocation,
				TwilightConvention: prayer.MWL(),
				AsrConvention:      prayer.Shafii,
				PreciseToSeconds:   true,
			}

			schedules, _ := prayer.Calculate(config, date.Year())
			dayOfYear := date.YearDay() - 1
			schedule := schedules[dayOfYear]

			asrTime := apkRoundedMinute(schedule.Asr)
			ishaTime := apkRoundedMinute(schedule.Isha)

			marker := ""
			for prayer, expectedTime := range testCase.expected {
				if prayer == "Asr" && asrTime == expectedTime {
					marker += " ✅Asr"
				}
				if prayer == "Isha" && ishaTime == expectedTime {
					marker += " ✅Isha"
				}
			}

			fmt.Printf("  %2.0fm: Asr=%s, Isha=%s%s\n", elevation, asrTime, ishaTime, marker)
		}

		// Test 2: Micro-adjustments to coordinates
		fmt.Println("\n🎯 Testing coordinate micro-adjustments:")
		coordAdjustments := []struct {
			name string
			lat  float64
			lon  float64
		}{
			{"Original", latitude, longitude},
			{"Lat+0.001", latitude + 0.001, longitude},
			{"Lat-0.001", latitude - 0.001, longitude},
			{"Lon+0.001", latitude, longitude + 0.001},
			{"Lon-0.001", latitude, longitude - 0.001},
		}

		for _, coord := range coordAdjustments {
			config := prayer.Config{
				Latitude:           coord.lat,
				Longitude:          coord.lon,
				Elevation:          0,
				Timezone:           shanghaiLocation,
				TwilightConvention: prayer.MWL(),
				AsrConvention:      prayer.Shafii,
				PreciseToSeconds:   true,
			}

			schedules, _ := prayer.Calculate(config, date.Year())
			dayOfYear := date.YearDay() - 1
			schedule := schedules[dayOfYear]

			asrTime := apkRoundedMinute(schedule.Asr)
			ishaTime := apkRoundedMinute(schedule.Isha)

			marker := ""
			for prayer, expectedTime := range testCase.expected {
				if prayer == "Asr" && asrTime == expectedTime {
					marker += " ✅Asr"
				}
				if prayer == "Isha" && ishaTime == expectedTime {
					marker += " ✅Isha"
				}
			}

			fmt.Printf("  %-12s: Asr=%s, Isha=%s%s\n", coord.name, asrTime, ishaTime, marker)
		}

		// Test 3: Different twilight angle micro-adjustments
		fmt.Println("\n🌅 Testing twilight angle micro-adjustments:")
		angleAdjustments := []struct {
			name      string
			fajrAngle float64
			ishaAngle float64
		}{
			{"MWL", 18.0, 17.0},
			{"Fajr-0.1", 17.9, 17.0},
			{"Fajr+0.1", 18.1, 17.0},
			{"Isha-0.1", 18.0, 16.9},
			{"Isha+0.1", 18.0, 17.1},
			{"Both-0.1", 17.9, 16.9},
		}

		for _, angles := range angleAdjustments {
			customConvention := &prayer.TwilightConvention{
				FajrAngle: angles.fajrAngle,
				IshaAngle: angles.ishaAngle,
			}

			config := prayer.Config{
				Latitude:           latitude,
				Longitude:          longitude,
				Elevation:          0,
				Timezone:           shanghaiLocation,
				TwilightConvention: customConvention,
				AsrConvention:      prayer.Shafii,
				PreciseToSeconds:   true,
			}

			schedules, _ := prayer.Calculate(config, date.Year())
			dayOfYear := date.YearDay() - 1
			schedule := schedules[dayOfYear]

			asrTime := apkRoundedMinute(schedule.Asr)
			ishaTime := apkRoundedMinute(schedule.Isha)

			marker := ""
			for prayer, expectedTime := range testCase.expected {
				if prayer == "Asr" && asrTime == expectedTime {
					marker += " ✅Asr"
				}
				if prayer == "Isha" && ishaTime == expectedTime {
					marker += " ✅Isha"
				}
			}

			fmt.Printf("  %-12s: Asr=%s, Isha=%s%s\n", angles.name, asrTime, ishaTime, marker)
		}
	}
}

// Test the exact seconds that cause the difference
func analyzeExactSeconds() {
	latitude := 23.118889
	longitude := 113.37

	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")

	fmt.Println("\n\n🔬 ANALYZING EXACT SECONDS FOR DIFFERENCES")
	fmt.Println("=" + strings.Repeat("=", 80))

	problematicDates := []string{"2025-08-03", "2025-08-20", "2025-08-14"}

	for _, dateStr := range problematicDates {
		date, _ := time.ParseInLocation("2006-01-02", dateStr, shanghaiLocation)

		config := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          0,
			Timezone:           shanghaiLocation,
			TwilightConvention: prayer.MWL(),
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}

		schedules, _ := prayer.Calculate(config, date.Year())
		dayOfYear := date.YearDay() - 1
		schedule := schedules[dayOfYear]

		fmt.Printf("\n📅 %s\n", dateStr)
		fmt.Println("-" + strings.Repeat("-", 50))

		// Analyze Asr
		asrSeconds := schedule.Asr.Second()
		asrFraction := float64(asrSeconds) / 60.0
		asrRounded := math.Round(asrFraction)

		fmt.Printf("Asr: %s\n", schedule.Asr.Format("15:04:05"))
		fmt.Printf("  Seconds: %d\n", asrSeconds)
		fmt.Printf("  Fraction: %.6f\n", asrFraction)
		fmt.Printf("  math.Round: %.0f\n", asrRounded)
		fmt.Printf("  APK result: %s\n", apkRoundedMinute(schedule.Asr))

		// Analyze Isha
		ishaSeconds := schedule.Isha.Second()
		ishaFraction := float64(ishaSeconds) / 60.0
		ishaRounded := math.Round(ishaFraction)

		fmt.Printf("Isha: %s\n", schedule.Isha.Format("15:04:05"))
		fmt.Printf("  Seconds: %d\n", ishaSeconds)
		fmt.Printf("  Fraction: %.6f\n", ishaFraction)
		fmt.Printf("  math.Round: %.0f\n", ishaRounded)
		fmt.Printf("  APK result: %s\n", apkRoundedMinute(schedule.Isha))

		// Show what would happen with different seconds
		fmt.Printf("What if seconds were different:\n")
		testSeconds := []int{25, 29, 30, 31, 35}
		for _, sec := range testSeconds {
			testTime := time.Date(schedule.Asr.Year(), schedule.Asr.Month(), schedule.Asr.Day(),
				schedule.Asr.Hour(), schedule.Asr.Minute(), sec, 0, schedule.Asr.Location())
			result := apkRoundedMinute(testTime)
			fmt.Printf("  %ds → %s\n", sec, result)
		}
	}
}

func main() {
	testPotentialDifferences()
	analyzeExactSeconds()

	fmt.Println("\n🎯 CONCLUSIONS:")
	fmt.Println("1. APK uses LibcRound (standard mathematical rounding)")
	fmt.Println("2. The 1-minute differences might be due to:")
	fmt.Println("   - Micro-differences in astronomical constants")
	fmt.Println("   - Different elevation handling")
	fmt.Println("   - Slightly different coordinate precision")
	fmt.Println("   - Different twilight angle values")
	fmt.Println("3. Need to find the exact parameter that APK uses differently")
}
