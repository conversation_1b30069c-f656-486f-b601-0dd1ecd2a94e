// Step-by-step debugging of APK algorithm vs expected results
package main

import (
	"fmt"
	"math"
	"time"
)

const (
	DEG_TO_RAD = math.Pi / 180.0
	RAD_TO_DEG = 180.0 / math.Pi
)

// Calculate Julian Day
func julianDay(date time.Time) float64 {
	utc := date.UTC()
	year := utc.Year()
	month := int(utc.Month())
	day := utc.Day()
	
	if month <= 2 {
		year--
		month += 12
	}
	
	a := year / 100
	b := 2 - a + a/4
	
	jd := math.Floor(365.25*float64(year+4716)) + 
		  math.Floor(30.6001*float64(month+1)) + 
		  float64(day) + float64(b) - 1524.5
	
	return jd
}

// Calculate solar coordinates
func solarCoordinates(jd float64) (declination, rightAscension, equationOfTime float64) {
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	
	lambda := L + 1.915*math.Sin(g*DEG_TO_RAD) + 0.020*math.Sin(2*g*DEG_TO_RAD)
	
	declination = math.Asin(math.Sin(23.439*DEG_TO_RAD)*math.Sin(lambda*DEG_TO_RAD)) * RAD_TO_DEG
	
	rightAscension = math.Atan2(math.Cos(23.439*DEG_TO_RAD)*math.Sin(lambda*DEG_TO_RAD),
		math.Cos(lambda*DEG_TO_RAD)) * RAD_TO_DEG
	
	equationOfTime = 4 * (L - rightAscension)
	
	return declination, rightAscension, equationOfTime
}

// Calculate hour angle for given elevation
func hourAngleForElevation(latitude, declination, elevation float64) float64 {
	latRad := latitude * DEG_TO_RAD
	decRad := declination * DEG_TO_RAD
	elevRad := elevation * DEG_TO_RAD
	
	numerator := math.Sin(elevRad) - math.Sin(latRad)*math.Sin(decRad)
	denominator := math.Cos(latRad) * math.Cos(decRad)
	
	if math.Abs(numerator/denominator) > 1 {
		return math.NaN()
	}
	
	return math.Acos(numerator/denominator) * RAD_TO_DEG
}

// Calculate Asr elevation angle
func asrElevationAngle(latitude, declination, shadowFactor float64) float64 {
	// APK's afternoon() algorithm
	decRad := declination * DEG_TO_RAD
	tanDec := math.Tan(decRad)
	
	// Core formula: atan(1 / (shadowFactor + tan(declination)))
	fraction := 1.0 / (shadowFactor + tanDec)
	asrAngle := math.Atan(fraction) * RAD_TO_DEG
	
	return asrAngle
}

// Convert hour angle to time
func hourAngleToTime(hourAngle, longitude, equationOfTime float64, date time.Time) time.Time {
	// Solar noon in UTC
	solarNoonUTC := 12.0 - longitude/15.0 - equationOfTime/60.0
	
	// Prayer time in UTC
	prayerTimeUTC := solarNoonUTC + hourAngle/15.0
	
	// Convert to local time (UTC+8 for Shanghai)
	prayerTimeLocal := prayerTimeUTC + 8.0
	
	// APK's floor-based conversion
	hours := int(math.Floor(prayerTimeLocal))
	minutesFloat := (prayerTimeLocal - float64(hours)) * 60.0
	minutes := int(math.Floor(minutesFloat))
	secondsFloat := (minutesFloat - float64(minutes)) * 60.0
	seconds := int(math.Floor(secondsFloat))
	
	// Handle day overflow
	if hours >= 24 {
		date = date.AddDate(0, 0, 1)
		hours -= 24
	} else if hours < 0 {
		date = date.AddDate(0, 0, -1)
		hours += 24
	}
	
	return time.Date(date.Year(), date.Month(), date.Day(), 
		hours, minutes, seconds, 0, date.Location())
}

// APK's rounding
func apkRound(t time.Time) string {
	seconds := float64(t.Second())
	fractionalMinutes := seconds / 60.0
	roundedMinutes := math.Round(fractionalMinutes)
	
	baseTime := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	adjustedTime := baseTime.Add(time.Duration(roundedMinutes) * time.Minute)
	
	return adjustedTime.Format("15:04")
}

func debugSpecificDate(dateStr string, latitude, longitude float64) {
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	date, _ := time.ParseInLocation("2006-01-02", dateStr, shanghaiLocation)
	
	fmt.Printf("🔍 DEBUGGING %s\n", dateStr)
	fmt.Println("=" + fmt.Sprintf("%*s", 50, "="))
	
	// Step 1: Julian Day
	jd := julianDay(date)
	fmt.Printf("Julian Day: %.6f\n", jd)
	
	// Step 2: Solar coordinates
	declination, rightAscension, equationOfTime := solarCoordinates(jd)
	fmt.Printf("Solar Declination: %.6f°\n", declination)
	fmt.Printf("Right Ascension: %.6f°\n", rightAscension)
	fmt.Printf("Equation of Time: %.6f minutes\n", equationOfTime)
	
	// Step 3: Calculate Asr
	fmt.Println("\n--- ASR CALCULATION ---")
	shadowFactor := 1.0 // Shafi
	asrElevation := asrElevationAngle(latitude, declination, shadowFactor)
	fmt.Printf("Asr Elevation Angle: %.6f°\n", asrElevation)
	
	asrHourAngle := hourAngleForElevation(latitude, declination, asrElevation)
	fmt.Printf("Asr Hour Angle: %.6f°\n", asrHourAngle)
	
	asrTime := hourAngleToTime(asrHourAngle, longitude, equationOfTime, date)
	fmt.Printf("Asr Time (precise): %s\n", asrTime.Format("15:04:05"))
	fmt.Printf("Asr Time (rounded): %s\n", apkRound(asrTime))
	
	// Step 4: Calculate Isha
	fmt.Println("\n--- ISHA CALCULATION ---")
	ishaElevation := -17.0 // MWL
	ishaHourAngle := hourAngleForElevation(latitude, declination, ishaElevation)
	fmt.Printf("Isha Hour Angle: %.6f°\n", ishaHourAngle)
	
	ishaTime := hourAngleToTime(ishaHourAngle, longitude, equationOfTime, date)
	fmt.Printf("Isha Time (precise): %s\n", ishaTime.Format("15:04:05"))
	fmt.Printf("Isha Time (rounded): %s\n", apkRound(ishaTime))
	
	// Step 5: Compare with expected
	fmt.Println("\n--- COMPARISON ---")
	expected := map[string]string{
		"2025-08-03": "Asr: 15:56",
		"2025-08-20": "Asr: 15:56", 
		"2025-08-14": "Isha: 20:14",
	}
	
	if exp, exists := expected[dateStr]; exists {
		fmt.Printf("Expected: %s\n", exp)
		if dateStr == "2025-08-14" {
			fmt.Printf("Actual Isha: %s\n", apkRound(ishaTime))
		} else {
			fmt.Printf("Actual Asr: %s\n", apkRound(asrTime))
		}
	}
	
	// Step 6: Test different parameters
	fmt.Println("\n--- PARAMETER TESTING ---")
	
	// Test different Isha angles
	if dateStr == "2025-08-14" {
		fmt.Println("Testing different Isha angles:")
		for _, angle := range []float64{-16.8, -16.9, -17.0, -17.1, -17.2} {
			testHourAngle := hourAngleForElevation(latitude, declination, angle)
			testTime := hourAngleToTime(testHourAngle, longitude, equationOfTime, date)
			fmt.Printf("  %.1f°: %s\n", angle, apkRound(testTime))
		}
	}
	
	// Test coordinate adjustments
	fmt.Println("Testing coordinate micro-adjustments:")
	for _, latAdj := range []float64{0, 0.001, -0.001} {
		for _, lonAdj := range []float64{0, 0.001, -0.001} {
			if latAdj == 0 && lonAdj == 0 {
				continue
			}
			
			testLat := latitude + latAdj
			testLon := longitude + lonAdj
			
			if dateStr == "2025-08-14" {
				testHourAngle := hourAngleForElevation(testLat, declination, -17.0)
				testTime := hourAngleToTime(testHourAngle, testLon, equationOfTime, date)
				fmt.Printf("  lat%+.3f,lon%+.3f: Isha %s\n", latAdj, lonAdj, apkRound(testTime))
			} else {
				testAsrElevation := asrElevationAngle(testLat, declination, 1.0)
				testHourAngle := hourAngleForElevation(testLat, declination, testAsrElevation)
				testTime := hourAngleToTime(testHourAngle, testLon, equationOfTime, date)
				fmt.Printf("  lat%+.3f,lon%+.3f: Asr %s\n", latAdj, lonAdj, apkRound(testTime))
			}
		}
	}
}

func main() {
	latitude := 23.118889
	longitude := 113.37
	
	// Debug each problematic date
	problematicDates := []string{"2025-08-03", "2025-08-20", "2025-08-14"}
	
	for i, dateStr := range problematicDates {
		if i > 0 {
			fmt.Println("\n" + fmt.Sprintf("%*s", 80, ""))
		}
		debugSpecificDate(dateStr, latitude, longitude)
	}
	
	fmt.Println("\n🎯 ANALYSIS COMPLETE")
	fmt.Println("Look for parameter combinations that give the expected results!")
}
