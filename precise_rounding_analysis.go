// Precise rounding analysis for prayer times
package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

// Different rounding methods
func roundToMinute(t time.Time) string {
	// Your method: >30 seconds -> next minute, ≤30 seconds -> current minute
	if t.Second() > 30 {
		return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
	}
	return t.Truncate(time.Minute).Format("15:04")
}

func floorToMinute(t time.Time) string {
	// Floor method: always truncate seconds
	return t.Truncate(time.Minute).Format("15:04")
}

func ceilToMinute(t time.Time) string {
	// Ceiling method: always round up if there are any seconds
	if t.Second() > 0 {
		return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
	}
	return t.Format("15:04")
}

func standardRoundToMinute(t time.Time) string {
	// Standard rounding: ≥30 seconds -> next minute
	if t.Second() >= 30 {
		return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
	}
	return t.Truncate(time.Minute).Format("15:04")
}

// Test different rounding methods on problematic dates
func testRoundingMethods() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	
	// Problematic dates
	testDates := []struct {
		date     string
		expected map[string]string
	}{
		{
			"2025-08-03",
			map[string]string{"Asr": "15:56"},
		},
		{
			"2025-08-20", 
			map[string]string{"Asr": "15:56"},
		},
		{
			"2025-08-14",
			map[string]string{"Isha": "20:14"},
		},
	}
	
	fmt.Println("🔍 PRECISE ROUNDING ANALYSIS")
	fmt.Println("=" + strings.Repeat("=", 90))
	
	for _, testCase := range testDates {
		date, _ := time.ParseInLocation("2006-01-02", testCase.date, shanghaiLocation)
		
		fmt.Printf("\n📅 %s\n", testCase.date)
		fmt.Println("-" + strings.Repeat("-", 70))
		
		// Get precise times
		config := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          0,
			Timezone:           shanghaiLocation,
			TwilightConvention: prayer.MWL(),
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}
		
		schedules, _ := prayer.Calculate(config, date.Year())
		dayOfYear := date.YearDay() - 1
		schedule := schedules[dayOfYear]
		
		// Show precise times
		fmt.Printf("Precise times:\n")
		fmt.Printf("  Fajr: %s\n", schedule.Fajr.Format("15:04:05"))
		fmt.Printf("  Asr:  %s\n", schedule.Asr.Format("15:04:05"))
		fmt.Printf("  Isha: %s\n", schedule.Isha.Format("15:04:05"))
		
		fmt.Printf("\nRounding methods:\n")
		fmt.Printf("  %-15s %-8s %-8s %-8s\n", "Method", "Fajr", "Asr", "Isha")
		fmt.Println("  " + strings.Repeat("-", 50))
		
		// Test different rounding methods
		methods := []struct {
			name string
			fn   func(time.Time) string
		}{
			{"Your method", roundToMinute},
			{"Floor", floorToMinute},
			{"Ceiling", ceilToMinute},
			{"Standard", standardRoundToMinute},
		}
		
		for _, method := range methods {
			fajr := method.fn(schedule.Fajr)
			asr := method.fn(schedule.Asr)
			isha := method.fn(schedule.Isha)
			
			// Check if this method gives expected results
			marker := ""
			for prayer, expectedTime := range testCase.expected {
				if prayer == "Asr" && asr == expectedTime {
					marker += " ✅Asr"
				}
				if prayer == "Isha" && isha == expectedTime {
					marker += " ✅Isha"
				}
			}
			
			fmt.Printf("  %-15s %-8s %-8s %-8s%s\n", 
				method.name, fajr, asr, isha, marker)
		}
	}
}

// Test with different twilight angles and rounding
func testAnglesWithRounding() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	
	fmt.Println("\n\n🧪 TESTING DIFFERENT ANGLES WITH ROUNDING")
	fmt.Println("=" + strings.Repeat("=", 90))
	
	// Test different twilight conventions
	conventions := []struct {
		name string
		conv *prayer.TwilightConvention
	}{
		{"MWL (18°,17°)", prayer.MWL()},
		{"Custom (17°,18°)", &prayer.TwilightConvention{FajrAngle: 17.0, IshaAngle: 18.0}},
		{"Test (17.5°,17.5°)", &prayer.TwilightConvention{FajrAngle: 17.5, IshaAngle: 17.5}},
	}
	
	testDate := "2025-08-14" // Focus on Isha problem
	date, _ := time.ParseInLocation("2006-01-02", testDate, shanghaiLocation)
	
	fmt.Printf("Testing %s for Isha target: 20:14\n\n", testDate)
	
	for _, conv := range conventions {
		fmt.Printf("📐 %s\n", conv.name)
		fmt.Println("-" + strings.Repeat("-", 50))
		
		config := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          0,
			Timezone:           shanghaiLocation,
			TwilightConvention: conv.conv,
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}
		
		schedules, _ := prayer.Calculate(config, date.Year())
		dayOfYear := date.YearDay() - 1
		schedule := schedules[dayOfYear]
		
		fmt.Printf("Precise Isha: %s\n", schedule.Isha.Format("15:04:05"))
		
		// Test rounding methods
		methods := []struct {
			name string
			fn   func(time.Time) string
		}{
			{"Your method", roundToMinute},
			{"Floor", floorToMinute},
			{"Standard", standardRoundToMinute},
		}
		
		for _, method := range methods {
			isha := method.fn(schedule.Isha)
			marker := ""
			if isha == "20:14" {
				marker = " ✅ TARGET!"
			}
			fmt.Printf("  %-12s: %s%s\n", method.name, isha, marker)
		}
		fmt.Println()
	}
}

// Test micro-adjustments to angles
func testMicroAdjustments() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	
	fmt.Println("\n🔬 TESTING MICRO-ADJUSTMENTS TO ANGLES")
	fmt.Println("=" + strings.Repeat("=", 90))
	
	// Test micro-adjustments around MWL values
	testCases := []struct {
		date   string
		target string
		prayer string
	}{
		{"2025-08-14", "20:14", "Isha"},
		{"2025-08-03", "15:56", "Asr"},
		{"2025-08-20", "15:56", "Asr"},
	}
	
	for _, testCase := range testCases {
		date, _ := time.ParseInLocation("2006-01-02", testCase.date, shanghaiLocation)
		
		fmt.Printf("\n📅 %s - Target %s: %s\n", testCase.date, testCase.prayer, testCase.target)
		fmt.Println("-" + strings.Repeat("-", 60))
		
		if testCase.prayer == "Isha" {
			// Test different Isha angles
			ishaAngles := []float64{16.8, 16.9, 17.0, 17.1, 17.2}
			
			fmt.Printf("%-10s %-15s %-8s\n", "Isha Angle", "Precise Time", "Rounded")
			fmt.Println("-" + strings.Repeat("-", 40))
			
			for _, angle := range ishaAngles {
				conv := &prayer.TwilightConvention{FajrAngle: 18.0, IshaAngle: angle}
				
				config := prayer.Config{
					Latitude:           latitude,
					Longitude:          longitude,
					Elevation:          0,
					Timezone:           shanghaiLocation,
					TwilightConvention: conv,
					AsrConvention:      prayer.Shafii,
					PreciseToSeconds:   true,
				}
				
				schedules, _ := prayer.Calculate(config, date.Year())
				dayOfYear := date.YearDay() - 1
				schedule := schedules[dayOfYear]
				
				precise := schedule.Isha.Format("15:04:05")
				rounded := roundToMinute(schedule.Isha)
				
				marker := ""
				if rounded == testCase.target {
					marker = " ✅"
				}
				
				fmt.Printf("%-10.1f %-15s %-8s%s\n", angle, precise, rounded, marker)
			}
		}
	}
}

func main() {
	testRoundingMethods()
	testAnglesWithRounding()
	testMicroAdjustments()
	
	fmt.Println("\n🎯 CONCLUSIONS:")
	fmt.Println("1. Your rounding method: >30s → next minute, ≤30s → current minute")
	fmt.Println("2. APK might use different twilight angles + specific rounding")
	fmt.Println("3. Small angle adjustments (0.1-0.2°) can shift times by 1 minute")
	fmt.Println("4. The key is finding the exact combination APK uses")
}
