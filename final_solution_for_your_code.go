// Final solution for your almost_perfect_prayer_times.go
package main

import (
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

// APK-style rounding methods based on reverse engineering analysis
func formatTimeAPKStyle(t time.Time, prayerType string) string {
	switch prayerType {
	case "Isha":
		// APK uses Floor rounding for Isha
		return t.Truncate(time.Minute).Format("15:04")
	case "Asr":
		// APK uses Ceiling rounding for Asr (if any seconds, round up)
		if t.Second() > 0 {
			return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
		}
		return t.Format("15:04")
	default:
		// Standard rounding for other prayers (>30s → next minute)
		if t.Second() > 30 {
			return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
		}
		return t.Truncate(time.Minute).Format("15:04")
	}
}

// Alternative: Use micro-adjusted angles
func getAPKTwilightConvention() *prayer.TwilightConvention {
	return &prayer.TwilightConvention{
		FajrAngle: 18.0,
		IshaAngle: 16.9, // Micro-adjusted from 17.0 to 16.9
	}
}

// Calculate Dhuha time using true 4.5° solar elevation angle (your existing perfect method)
func calculateDhuhaTime(date time.Time, latitude, longitude float64, sunrise time.Time) time.Time {
	// Get astronomical parameters
	jd := getJulianDayNumber(date)
	declination := getSolarDeclinationAngle(jd)
	
	// Calculate hour angles for sunrise (-0.833°) and Dhuha (4.5°)
	sunriseHourAngle := getHourAngleForElevation(latitude, declination, -0.833)
	dhuhaHourAngle := getHourAngleForElevation(latitude, declination, 4.5)
	
	if math.IsNaN(sunriseHourAngle) || math.IsNaN(dhuhaHourAngle) {
		// Fallback to approximate 24 minutes if calculation fails
		return sunrise.Add(24 * time.Minute)
	}
	
	// Calculate time difference in hours
	timeDiffHours := (sunriseHourAngle - dhuhaHourAngle) / 15.0
	
	// Add the time difference to sunrise
	return sunrise.Add(time.Duration(timeDiffHours * 3600) * time.Second)
}

// Helper functions (same as your existing code)
func getJulianDayNumber(date time.Time) float64 {
	utc := date.UTC()
	year := utc.Year()
	month := int(utc.Month())
	day := utc.Day()
	
	if month <= 2 {
		year--
		month += 12
	}
	
	a := year / 100
	b := 2 - a + a/4
	
	jd := math.Floor(365.25*float64(year+4716)) + 
		  math.Floor(30.6001*float64(month+1)) + 
		  float64(day) + float64(b) - 1524.5
	
	return jd
}

func getSolarDeclinationAngle(jd float64) float64 {
	n := jd - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := L + 1.915*math.Sin(g*math.Pi/180.0) + 0.020*math.Sin(2*g*math.Pi/180.0)
	
	return math.Asin(math.Sin(23.439*math.Pi/180.0)*math.Sin(lambda*math.Pi/180.0)) * 180.0/math.Pi
}

func getHourAngleForElevation(latitude, declination, elevation float64) float64 {
	latRad := latitude * math.Pi / 180.0
	decRad := declination * math.Pi / 180.0
	elevRad := elevation * math.Pi / 180.0
	
	numerator := math.Sin(elevRad) - math.Sin(latRad)*math.Sin(decRad)
	denominator := math.Cos(latRad) * math.Cos(decRad)
	
	if math.Abs(numerator/denominator) > 1 {
		return math.NaN()
	}
	
	return math.Acos(numerator/denominator) * 180.0/math.Pi
}

// Test both solutions
func testBothSolutions() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	
	testDates := []string{"2025-08-03", "2025-08-20", "2025-08-14"}
	
	fmt.Println("🔧 FINAL SOLUTIONS FOR YOUR CODE")
	fmt.Println("=" + strings.Repeat("=", 80))
	
	for _, dateStr := range testDates {
		date, _ := time.ParseInLocation("2006-01-02", dateStr, shanghaiLocation)
		
		fmt.Printf("\n📅 %s\n", dateStr)
		fmt.Println("-" + strings.Repeat("-", 60))
		
		// Solution 1: APK-style mixed rounding
		fmt.Println("🔧 Solution 1: APK-style Mixed Rounding")
		
		config1 := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          0,
			Timezone:           shanghaiLocation,
			TwilightConvention: prayer.MWL(),
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}
		
		schedules1, _ := prayer.Calculate(config1, date.Year())
		dayOfYear := date.YearDay() - 1
		schedule1 := schedules1[dayOfYear]
		
		// Calculate Dhuha
		dhuha1 := calculateDhuhaTime(date, latitude, longitude, schedule1.Sunrise)
		
		fmt.Printf("  Imsak:   %s\n", formatTimeAPKStyle(schedule1.Fajr.Add(-10*time.Minute), "other"))
		fmt.Printf("  Subuh:   %s\n", formatTimeAPKStyle(schedule1.Fajr, "other"))
		fmt.Printf("  Terbit:  %s\n", formatTimeAPKStyle(schedule1.Sunrise, "other"))
		fmt.Printf("  Dhuha:   %s\n", formatTimeAPKStyle(dhuha1, "other"))
		fmt.Printf("  Zuhur:   %s\n", formatTimeAPKStyle(schedule1.Zuhr.Add(2*time.Minute), "other"))
		fmt.Printf("  Ashar:   %s\n", formatTimeAPKStyle(schedule1.Asr, "Asr"))
		fmt.Printf("  Maghrib: %s\n", formatTimeAPKStyle(schedule1.Maghrib, "other"))
		fmt.Printf("  Isya':   %s\n", formatTimeAPKStyle(schedule1.Isha, "Isha"))
		
		// Solution 2: Micro-adjusted angles
		fmt.Println("\n🔧 Solution 2: Micro-adjusted Isha Angle (16.9°)")
		
		config2 := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          0,
			Timezone:           shanghaiLocation,
			TwilightConvention: getAPKTwilightConvention(),
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}
		
		schedules2, _ := prayer.Calculate(config2, date.Year())
		schedule2 := schedules2[dayOfYear]
		
		// Calculate Dhuha
		dhuha2 := calculateDhuhaTime(date, latitude, longitude, schedule2.Sunrise)
		
		// Use your original rounding method
		formatTimeOriginal := func(t time.Time) string {
			if t.Second() > 30 {
				return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
			}
			return t.Truncate(time.Minute).Format("15:04")
		}
		
		fmt.Printf("  Imsak:   %s\n", formatTimeOriginal(schedule2.Fajr.Add(-10*time.Minute)))
		fmt.Printf("  Subuh:   %s\n", formatTimeOriginal(schedule2.Fajr))
		fmt.Printf("  Terbit:  %s\n", formatTimeOriginal(schedule2.Sunrise))
		fmt.Printf("  Dhuha:   %s\n", formatTimeOriginal(dhuha2))
		fmt.Printf("  Zuhur:   %s\n", formatTimeOriginal(schedule2.Zuhr.Add(2*time.Minute)))
		fmt.Printf("  Ashar:   %s\n", formatTimeOriginal(schedule2.Asr))
		fmt.Printf("  Maghrib: %s\n", formatTimeOriginal(schedule2.Maghrib))
		fmt.Printf("  Isya':   %s\n", formatTimeOriginal(schedule2.Isha))
	}
	
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📋 TARGET RESULTS:")
	fmt.Println("2025-08-03: Asr should be 15:56")
	fmt.Println("2025-08-20: Asr should be 15:56") 
	fmt.Println("2025-08-14: Isha should be 20:14")
}

func main() {
	testBothSolutions()
	
	fmt.Println("\n🎯 RECOMMENDATIONS FOR YOUR CODE:")
	fmt.Println("1. **Solution 1**: Use APK-style mixed rounding:")
	fmt.Println("   - Isha: Floor rounding (always truncate)")
	fmt.Println("   - Asr: Ceiling rounding (round up if any seconds)")
	fmt.Println("   - Others: Your current method")
	fmt.Println()
	fmt.Println("2. **Solution 2**: Use micro-adjusted Isha angle:")
	fmt.Println("   - Change Isha angle from 17.0° to 16.9°")
	fmt.Println("   - Keep your current rounding method")
	fmt.Println()
	fmt.Println("3. **Keep your perfect Dhuha calculation** (4.5° method)")
	fmt.Println("4. **Keep your Zuhr +2 minutes adjustment**")
}
