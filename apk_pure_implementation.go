// Pure APK implementation based on reverse engineering
// No external prayer libraries - complete reimplementation
package main

import (
	"fmt"
	"math"
	"time"
)

const (
	// APK constants from reverse engineering
	DEG_TO_RAD = 0.017453292519943295 // π/180 (from 0x82cddc)
	RAD_TO_DEG = 57.29577951308232    // 180/π (from 0x82ce78)

	// Atmospheric refraction constant (from 0x82de28)
	ATMOSPHERIC_REFRACTION = -0.8333333333333334

	// Shadow length factors (from shadow_length.dart)
	SHAFI_SHADOW_FACTOR  = 1.0 // single()
	HANAFI_SHADOW_FACTOR = 2.0 // double()
)

// APK's SolarCoordinates implementation
type SolarCoordinates struct {
	declination          float64
	rightAscension       float64
	apparentSiderealTime float64
}

// APK's Observer implementation
type Observer struct {
	latitude  float64
	longitude float64
	elevation float64
}

// Calculate Julian Day (from CalendricalHelper.julianDayByDate)
func julianDayByDate(date time.Time) float64 {
	utc := date.UTC()
	year := utc.Year()
	month := int(utc.Month())
	day := utc.Day()

	if month <= 2 {
		year--
		month += 12
	}

	a := year / 100
	b := 2 - a + a/4

	jd := math.Floor(365.25*float64(year+4716)) +
		math.Floor(30.6001*float64(month+1)) +
		float64(day) + float64(b) - 1524.5

	return jd
}

// APK's SolarCoordinates calculation
func newSolarCoordinates(julianDay float64) *SolarCoordinates {
	// Based on APK's SolarCoordinates constructor
	n := julianDay - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)

	// Solar longitude
	lambda := L + 1.915*math.Sin(g*DEG_TO_RAD) + 0.020*math.Sin(2*g*DEG_TO_RAD)

	// Solar declination
	declination := math.Asin(math.Sin(23.439*DEG_TO_RAD)*math.Sin(lambda*DEG_TO_RAD)) * RAD_TO_DEG

	// Right ascension
	rightAscension := math.Atan2(math.Cos(23.439*DEG_TO_RAD)*math.Sin(lambda*DEG_TO_RAD),
		math.Cos(lambda*DEG_TO_RAD)) * RAD_TO_DEG

	// Apparent sidereal time
	apparentSiderealTime := 280.16 + 360.9856235*n

	return &SolarCoordinates{
		declination:          declination,
		rightAscension:       rightAscension,
		apparentSiderealTime: apparentSiderealTime,
	}
}

// APK's correctedHourAngle implementation (from astronomical.dart)
func correctedHourAngle(observer *Observer, solar *SolarCoordinates,
	angle float64, afterTransit bool) float64 {

	latRad := observer.latitude * DEG_TO_RAD
	decRad := solar.declination * DEG_TO_RAD
	angleRad := angle * DEG_TO_RAD

	// Core hour angle calculation
	numerator := math.Sin(angleRad) - math.Sin(latRad)*math.Sin(decRad)
	denominator := math.Cos(latRad) * math.Cos(decRad)

	if math.Abs(numerator/denominator) > 1 {
		return math.NaN()
	}

	hourAngle := math.Acos(numerator/denominator) * RAD_TO_DEG

	// APK applies elevation correction here
	if observer.elevation > 0 {
		// Elevation correction formula from APK
		elevationCorrection := math.Sqrt(observer.elevation) * 0.0347
		if afterTransit {
			hourAngle += elevationCorrection
		} else {
			hourAngle -= elevationCorrection
		}
	}

	return hourAngle
}

// APK's afternoon() function implementation (from solar_time.dart)
func calculateAsrTime(observer *Observer, solar *SolarCoordinates, shadowFactor float64) float64 {
	// Get solar declination in radians (from 0x82cd80-0x82cdac)
	declinationRad := solar.declination * DEG_TO_RAD

	// Calculate tan(declination) (from 0x82cdfc: CallRuntime_LibcTan)
	tanDeclination := math.Tan(declinationRad)

	// Core Asr formula: 1 / (shadowFactor + tan(declination))
	// From 0x82ce30-0x82ce38: fadd d2, d0, d1; fdiv d1, d0, d2
	denominator := shadowFactor + tanDeclination
	fraction := 1.0 / denominator

	// Calculate atan (from 0x82ce48: CallRuntime_LibcAtan)
	asrAngle := math.Atan(fraction) * RAD_TO_DEG

	// Convert to hour angle using correctedHourAngle
	hourAngle := correctedHourAngle(observer, solar, asrAngle, true)

	return hourAngle
}

// APK's twilight time calculation
func calculateTwilightTime(observer *Observer, solar *SolarCoordinates,
	angle float64, afterTransit bool) float64 {

	hourAngle := correctedHourAngle(observer, solar, angle, afterTransit)
	return hourAngle
}

// APK's time conversion using TimeComponents.fromDouble algorithm
func hourAngleToTime(observer *Observer, solar *SolarCoordinates, hourAngle float64,
	date time.Time, afterTransit bool) time.Time {

	// Calculate equation of time
	n := julianDayByDate(date) - 2451545.0
	L := math.Mod(280.460+0.9856474*n, 360.0)
	g := math.Mod(357.528+0.9856003*n, 360.0)
	lambda := L + 1.915*math.Sin(g*DEG_TO_RAD) + 0.020*math.Sin(2*g*DEG_TO_RAD)

	alpha := math.Atan2(math.Cos(23.439*DEG_TO_RAD)*math.Sin(lambda*DEG_TO_RAD),
		math.Cos(lambda*DEG_TO_RAD)) * RAD_TO_DEG

	equationOfTime := 4 * (L - alpha) // in minutes

	// Calculate solar noon in UTC
	solarNoonUTC := 12.0 - observer.longitude/15.0 - equationOfTime/60.0

	// Calculate prayer time in UTC
	var prayerTimeUTC float64
	if afterTransit {
		prayerTimeUTC = solarNoonUTC + hourAngle/15.0
	} else {
		prayerTimeUTC = solarNoonUTC - hourAngle/15.0
	}

	// Convert to local time (add timezone offset)
	// For Asia/Shanghai, UTC+8
	prayerTimeLocal := prayerTimeUTC + 8.0

	// APK's TimeComponents.fromDouble algorithm using fcvtms (floor)
	// From 0x82d8f4: fcvtms x0, d0 (floor conversion for hours)
	hours := int(math.Floor(prayerTimeLocal))

	// From 0x82d91c-0x82d92c: calculate minutes using floor
	hoursFraction := prayerTimeLocal - float64(hours)
	minutesFloat := hoursFraction * 60.0
	minutes := int(math.Floor(minutesFloat))

	// From 0x82d958-0x82d970: calculate seconds using floor
	minutesFraction := minutesFloat - float64(minutes)
	secondsFloat := minutesFraction * 60.0
	seconds := int(math.Floor(secondsFloat))

	// Handle day overflow
	if hours >= 24 {
		date = date.AddDate(0, 0, 1)
		hours -= 24
	} else if hours < 0 {
		date = date.AddDate(0, 0, -1)
		hours += 24
	}

	return time.Date(date.Year(), date.Month(), date.Day(),
		hours, minutes, seconds, 0, date.Location())
}

// APK's roundedMinute implementation (from calendar_util.dart)
func apkRoundedMinute(t time.Time) string {
	// From 0x82bb24: CalendarUtil.roundedMinute
	// Uses LibcRound for standard mathematical rounding
	seconds := float64(t.Second())
	fractionalMinutes := seconds / 60.0
	roundedMinutes := math.Round(fractionalMinutes)

	baseTime := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	adjustedTime := baseTime.Add(time.Duration(roundedMinutes) * time.Minute)

	return adjustedTime.Format("15:04")
}

// Complete APK prayer times calculation
func calculateAPKPrayerTimes(date time.Time, latitude, longitude, elevation float64) map[string]string {
	observer := &Observer{
		latitude:  latitude,
		longitude: longitude,
		elevation: elevation,
	}

	// Calculate solar coordinates for the date
	jd := julianDayByDate(date)
	solar := newSolarCoordinates(jd)

	// Calculate prayer times using APK's exact algorithms

	// Fajr: -18° (MWL convention)
	fajrHourAngle := calculateTwilightTime(observer, solar, -18.0, false)
	fajr := hourAngleToTime(observer, solar, fajrHourAngle, date, false)

	// Sunrise: -0.833° (atmospheric refraction)
	sunriseHourAngle := calculateTwilightTime(observer, solar, ATMOSPHERIC_REFRACTION, false)
	sunrise := hourAngleToTime(observer, solar, sunriseHourAngle, date, false)

	// Zuhr: Solar noon + 2 minutes (as found in your analysis)
	zuhr := hourAngleToTime(observer, solar, 0, date, true).Add(2 * time.Minute)

	// Asr: Using Shafi method (shadow factor = 1)
	asrHourAngle := calculateAsrTime(observer, solar, SHAFI_SHADOW_FACTOR)
	asr := hourAngleToTime(observer, solar, asrHourAngle, date, true)

	// Maghrib: Same as sunset
	maghrib := hourAngleToTime(observer, solar, sunriseHourAngle, date, true)

	// Isha: -17° (MWL convention)
	ishaHourAngle := calculateTwilightTime(observer, solar, -17.0, true)
	isha := hourAngleToTime(observer, solar, ishaHourAngle, date, true)

	// Format using APK's rounding method
	return map[string]string{
		"Imsak":   apkRoundedMinute(fajr.Add(-10 * time.Minute)),
		"Subuh":   apkRoundedMinute(fajr),
		"Terbit":  apkRoundedMinute(sunrise),
		"Zuhur":   apkRoundedMinute(zuhr),
		"Ashar":   apkRoundedMinute(asr),
		"Maghrib": apkRoundedMinute(maghrib),
		"Isya'":   apkRoundedMinute(isha),
	}
}

func main() {
	// Test the pure APK implementation
	latitude := 23.118889
	longitude := 113.37
	elevation := 0.0

	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")

	fmt.Println("🔥 PURE APK IMPLEMENTATION - NO EXTERNAL LIBRARIES")
	fmt.Println("=" + fmt.Sprintf("%*s", 60, "="))

	// Test problematic dates
	testDates := []string{"2025-08-03", "2025-08-20", "2025-08-14"}

	for _, dateStr := range testDates {
		date, _ := time.ParseInLocation("2006-01-02", dateStr, shanghaiLocation)

		result := calculateAPKPrayerTimes(date, latitude, longitude, elevation)

		fmt.Printf("\n📅 %s\n", dateStr)
		fmt.Println("-" + fmt.Sprintf("%*s", 40, "-"))
		fmt.Printf("Imsak:   %s\n", result["Imsak"])
		fmt.Printf("Subuh:   %s\n", result["Subuh"])
		fmt.Printf("Terbit:  %s\n", result["Terbit"])
		fmt.Printf("Zuhur:   %s\n", result["Zuhur"])
		fmt.Printf("Ashar:   %s\n", result["Ashar"])
		fmt.Printf("Maghrib: %s\n", result["Maghrib"])
		fmt.Printf("Isya':   %s\n", result["Isya'"])
	}

	fmt.Println("\n🎯 This is a complete reimplementation based on APK's assembly code!")
	fmt.Println("No external prayer libraries used - pure reverse engineering!")
}
