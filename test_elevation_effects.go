// Test elevation effects on prayer times
package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/hablullah/go-prayer"
)

// Test elevation effects on Asr times
func testElevationEffects() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	
	// Test problematic dates
	testDates := []string{"2025-08-03", "2025-08-20", "2025-08-14"}
	
	// Test different elevations
	elevations := []float64{0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100}
	
	fmt.Println("🏔️ TESTING ELEVATION EFFECTS ON PRAYER TIMES")
	fmt.Println("=" + strings.Repeat("=", 80))
	
	for _, dateStr := range testDates {
		date, _ := time.ParseInLocation("2006-01-02", dateStr, shanghaiLocation)
		
		fmt.Printf("\n📅 %s\n", dateStr)
		fmt.Println("-" + strings.Repeat("-", 60))
		fmt.Printf("%-10s %-8s %-8s %-8s\n", "Elevation", "Fajr", "Asr", "Isha")
		fmt.Println("-" + strings.Repeat("-", 60))
		
		for _, elevation := range elevations {
			config := prayer.Config{
				Latitude:           latitude,
				Longitude:          longitude,
				Elevation:          elevation,
				Timezone:           shanghaiLocation,
				TwilightConvention: prayer.MWL(),
				AsrConvention:      prayer.Shafii,
				PreciseToSeconds:   true,
			}
			
			schedules, _ := prayer.Calculate(config, date.Year())
			dayOfYear := date.YearDay() - 1
			schedule := schedules[dayOfYear]
			
			// Format times using the same method as your code
			formatTime := func(t time.Time) string {
				if t.Second() > 30 {
					return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
				}
				return t.Format("15:04")
			}
			
			fmt.Printf("%-10.0fm %-8s %-8s %-8s\n", 
				elevation,
				formatTime(schedule.Fajr),
				formatTime(schedule.Asr),
				formatTime(schedule.Isha))
		}
	}
	
	// Expected results for comparison
	fmt.Println("\n" + strings.Repeat("=", 80))
	fmt.Println("📋 TARGET RESULTS:")
	fmt.Println("2025-08-03: Asr should be 15:56 (currently 15:55)")
	fmt.Println("2025-08-20: Asr should be 15:56 (currently 15:55)")
	fmt.Println("2025-08-14: Isha should be 20:14 (currently 20:15)")
}

// Test specific elevation that might give us the target times
func testSpecificElevations() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	
	fmt.Println("\n🎯 TESTING SPECIFIC ELEVATIONS FOR TARGET TIMES")
	fmt.Println("=" + strings.Repeat("=", 80))
	
	// Test elevations that might give us +1 minute for Asr
	specificElevations := []float64{15, 25, 35, 45, 55, 65, 75, 85, 95}
	
	testCases := []struct {
		date   string
		target string
		prayer string
	}{
		{"2025-08-03", "15:56", "Asr"},
		{"2025-08-20", "15:56", "Asr"},
		{"2025-08-14", "20:14", "Isha"},
	}
	
	for _, testCase := range testCases {
		date, _ := time.ParseInLocation("2006-01-02", testCase.date, shanghaiLocation)
		
		fmt.Printf("\n📅 %s - Target %s: %s\n", testCase.date, testCase.prayer, testCase.target)
		fmt.Println("-" + strings.Repeat("-", 50))
		fmt.Printf("%-10s %-8s %-8s %-8s\n", "Elevation", "Fajr", "Asr", "Isha")
		fmt.Println("-" + strings.Repeat("-", 50))
		
		for _, elevation := range specificElevations {
			config := prayer.Config{
				Latitude:           latitude,
				Longitude:          longitude,
				Elevation:          elevation,
				Timezone:           shanghaiLocation,
				TwilightConvention: prayer.MWL(),
				AsrConvention:      prayer.Shafii,
				PreciseToSeconds:   true,
			}
			
			schedules, _ := prayer.Calculate(config, date.Year())
			dayOfYear := date.YearDay() - 1
			schedule := schedules[dayOfYear]
			
			// Format times using the same method as your code
			formatTime := func(t time.Time) string {
				if t.Second() > 30 {
					return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
				}
				return t.Format("15:04")
			}
			
			asrTime := formatTime(schedule.Asr)
			ishaTime := formatTime(schedule.Isha)
			
			// Highlight if we hit the target
			marker := ""
			if (testCase.prayer == "Asr" && asrTime == testCase.target) ||
			   (testCase.prayer == "Isha" && ishaTime == testCase.target) {
				marker = " ✅ TARGET!"
			}
			
			fmt.Printf("%-10.0fm %-8s %-8s %-8s%s\n", 
				elevation,
				formatTime(schedule.Fajr),
				asrTime,
				ishaTime,
				marker)
		}
	}
}

// Test combination of custom angles + elevation
func testCombinedEffects() {
	latitude := 23.118889
	longitude := 113.37
	
	shanghaiLocation, _ := time.LoadLocation("Asia/Shanghai")
	date, _ := time.ParseInLocation("2006-01-02", "2025-08-14", shanghaiLocation)
	
	fmt.Println("\n🔬 TESTING COMBINED EFFECTS (Custom Angles + Elevation)")
	fmt.Println("=" + strings.Repeat("=", 80))
	fmt.Println("Testing 2025-08-14 for Isha target: 20:14")
	fmt.Println()
	
	// Custom twilight convention (APK angles)
	customConvention := &prayer.TwilightConvention{
		FajrAngle: 17.0, // APK uses 17° for Fajr
		IshaAngle: 18.0, // APK uses 18° for Isha
	}
	
	elevations := []float64{0, 10, 20, 30, 40, 50}
	
	fmt.Printf("%-10s %-15s %-15s\n", "Elevation", "Standard MWL", "APK Custom")
	fmt.Println("-" + strings.Repeat("-", 50))
	
	for _, elevation := range elevations {
		// Standard MWL
		configMWL := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          elevation,
			Timezone:           shanghaiLocation,
			TwilightConvention: prayer.MWL(),
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}
		
		schedulesMWL, _ := prayer.Calculate(configMWL, date.Year())
		dayOfYear := date.YearDay() - 1
		scheduleMWL := schedulesMWL[dayOfYear]
		
		// Custom APK
		configCustom := prayer.Config{
			Latitude:           latitude,
			Longitude:          longitude,
			Elevation:          elevation,
			Timezone:           shanghaiLocation,
			TwilightConvention: customConvention,
			AsrConvention:      prayer.Shafii,
			PreciseToSeconds:   true,
		}
		
		schedulesCustom, _ := prayer.Calculate(configCustom, date.Year())
		scheduleCustom := schedulesCustom[dayOfYear]
		
		formatTime := func(t time.Time) string {
			if t.Second() > 30 {
				return t.Add(time.Minute).Truncate(time.Minute).Format("15:04")
			}
			return t.Format("15:04")
		}
		
		mwlIsha := formatTime(scheduleMWL.Isha)
		customIsha := formatTime(scheduleCustom.Isha)
		
		// Highlight if we hit the target
		mwlMarker := ""
		customMarker := ""
		if mwlIsha == "20:14" {
			mwlMarker = " ✅"
		}
		if customIsha == "20:14" {
			customMarker = " ✅"
		}
		
		fmt.Printf("%-10.0fm %-15s %-15s\n", 
			elevation,
			mwlIsha + mwlMarker,
			customIsha + customMarker)
	}
}

func main() {
	testElevationEffects()
	testSpecificElevations()
	testCombinedEffects()
	
	fmt.Println("\n🎯 ANALYSIS:")
	fmt.Println("1. Check if elevation affects Asr times")
	fmt.Println("2. Look for elevation values that give target times")
	fmt.Println("3. Test combination of custom angles + elevation")
	fmt.Println("4. The APK might use a specific elevation setting")
}
